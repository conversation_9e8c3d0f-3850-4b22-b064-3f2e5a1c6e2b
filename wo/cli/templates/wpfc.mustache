# WPFC NGINX CONFIGURATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
# $skip_cache variable set in /etc/nginx/conf.d/map-wp.conf

add_header X-fastcgi-cache $upstream_cache_status;
# Use cached or actual file if they exists, Otherwise pass request to WordPress
location / {
  try_files $uri $uri/ /index.php$is_args$args;
}
location ~ \.php$ {
  try_files $uri =404;
  include fastcgi_params;
  fastcgi_pass {{upstream}};
  fastcgi_cache_bypass $skip_cache;
  fastcgi_no_cache $skip_cache;
  fastcgi_cache WORDPRESS;
}
location ~ /purge(/.*) {
  fastcgi_cache_purge WORDPRESS "$scheme$request_method$host$1";
  access_log off;
}
