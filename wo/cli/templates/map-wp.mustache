# NGINX CONFIGURATION FOR FASTCGI_CACHE EXCEPTION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE

# do not cache xhtml request
map $http_x_requested_with $http_request_no_cache {
    default 0;
    XMLHttpRequest 1;
}

# do not cache requests with Authorization: header set
map $http_authorization $http_auth_no_cache {
    default 1;
    "" 0;
}

# do not cache requests on cookies
map $http_cookie $cookie_no_cache {
    default 0;
    "~*wordpress_[a-f0-9]+" 1;
    "~*wp-postpass" 1;
    "~*wordpress_logged_in" 1;
    "~*wordpress_no_cache" 1;
    "~*comment_author" 1;
    "~*woocommerce_items_in_cart" 1;
    "~*edd_items_in_cart" 1;
    "~*woocommerce_cart_hash" 1;
    "~*wptouch_switch_toogle" 1;
    "~*comment_author_email_" 1;
    "~*wptouch_switch_toggle" 1;
    "~*edd" 1;
}

# do not cache the following uri
map $request_uri $uri_no_cache {
    default 0;
    "~*/wishlist/" 1;
    "~*/wp-admin/" 1;
    "~*/wp-[a-zA-Z0-9-]+\.php" 1;
    "~*/feed/" 1;
    "~*/index\.php" 1;
    "~*/[a-z0-9_-]+-sitemap([0-9]+)?\.xml" 1;
    "~*/sitemap(_index)?\.xml" 1;
    "~*/wp-comments-popup\.php" 1;
    "~*/wp-links-opml\.php" 1;
    "~*/xmlrpc\.php" 1;
    "~*/edd-sl/.*" 1;
    "~*/add_to_cart/" 1;
    "~*/cart/" 1;
    "~*/account/" 1;
    "~*/my-account/" 1;
    "~*/checkout/" 1;
    "~*/addons/" 1;
    "~*/wc-api/.*" 1;
    "~*/logout/" 1;
    "~*/lost-password/" 1;
    "~*/panier/" 1;
    "~*/mon-compte/" 1;
    "~*/embed" 1;
    "~*/commande/" 1;
    "~*/resetpass/" 1;
    "~*/wp.serviceworker" 1;
}
# mobile_prefix needed for WP-Rocket
map $http_user_agent $mobile_prefix {
    default "";
    "~*iphone" -mobile;
    "~*android" -mobile;
}

# do not cache requests with query strings
map $is_args $is_args_no_cache {
    default 1;
    "" 0;
}

# cache requests with query string related to analytics
map $args $args_to_cache {
    default 0;
    "~*utm_" 1;
    "~*fbclid" 1;
}

# do not cache requests with query strings excepted analytics related queries
map $is_args_no_cache$args_to_cache $query_no_cache {
    defaut 1;
    00 0;
    11 0;
}

# if all previous check are passed, $skip_cache = 0
map $http_request_no_cache$http_auth_no_cache$cookie_no_cache$uri_no_cache$query_no_cache $skip_cache {
    default 1;
    00000 0;
}

# map $skip_cache with $cache_uri for --wpsc --wpce & --wprocket stack
map $skip_cache $cache_uri {
    0 $request_uri;
    default 'null cache';
}

# http_prefix needed for WP-Rocket
map $https $https_prefix {
    default "";
    on "-https";
}

# needed to proxy web-socket connections
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}
