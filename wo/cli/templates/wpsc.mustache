# WPSC NGINX CONFIGURATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
# $cache_uri variable set in /etc/nginx/conf.d/map-wp.conf

# Use cached or actual file if they exists, Otherwise pass request to WordPress
location / {
  # If we add index.php?$args its break WooCommerce like plugins
  # Ref: #330
  try_files /wp-content/cache/supercache/$http_host/$cache_uri/index${mobile_prefix}${https_prefix}.html /wp-content/cache/supercache/$http_host/$cache_uri/index${https_prefix}.html $uri $uri/ /index.php$is_args$args;
}
location ~ \.php$ {
  try_files $uri =404;
  include fastcgi_params;
  fastcgi_pass {{upstream}};
  # Following line is needed by WP Super Cache plugin
  fastcgi_param SERVER_NAME $http_host;
}
