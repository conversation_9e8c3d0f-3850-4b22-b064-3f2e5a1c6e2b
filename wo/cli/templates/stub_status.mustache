# Stub status NGINX CONFIGURATION
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
{{#phpconf}}
upstream phpstatus {
    server unix:/run/php/php72-fpm.sock;
}
upstream php73opcache {
    server unix:/run/php/php73-fpm.sock;
}
upstream php74opcache {
    server unix:/run/php/php74-fpm.sock;
}
{{/phpconf}}
server {
    listen 127.0.0.1:80;
    server_name 127.0.0.1 localhost;
    access_log off;
    log_not_found off;
    root /var/www/22222/htdocs;
    allow 127.0.0.1;
    deny all;
    location ~ /(stub_status|nginx_status) {
        stub_status on;
        allow 127.0.0.1;
        deny all;
        access_log off;
        log_not_found off;
    }
    {{#phpconf}}
    location ~ /(status|ping) {
        include fastcgi_params;
        fastcgi_pass phpstatus;
        access_log off;
        log_not_found off;
    }
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }
    location /cache/opcache/php72.php {
        try_files $uri =404;
        include fastcgi_params;
        fastcgi_pass phpstatus;
        access_log off;
        log_not_found off;
    }
    location /cache/opcache/php73.php {
        try_files $uri =404;
        include fastcgi_params;
        fastcgi_pass php73opcache;
        access_log off;
        log_not_found off;
    }
    location /cache/opcache/php74.php {
        try_files $uri =404;
        include fastcgi_params;
        fastcgi_pass php74opcache;
        access_log off;
        log_not_found off;
    }
    {{/phpconf}}
}
