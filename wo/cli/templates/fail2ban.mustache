[DEFAULT]
ignoreip = 127.0.0.1/8 ::1

[recidive]
enabled = true

{{#nginx}}[nginx-http-auth]
enabled   = true
logpath = /var/log/nginx/*error*.log

[nginx-botsearch]
enabled   = true
logpath = /var/log/nginx/*access*.log

[wo-wordpress]
enabled = true
filter = wo-wordpress
action = iptables-multiport[name="wo-wordpress", port="http,https"]
logpath = /var/log/nginx/*access*.log
maxretry = 5

[nginx-forbidden]
enabled = true
filter = nginx-forbidden
action = iptables-multiport[name="nginx-forbidden", port="http,https"]
logpath = /var/log/nginx/*error*.log{{/nginx}}