# Redis NGINX CONFIGURATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
# $skip_cache variable set in /etc/nginx/conf.d/map-wp.conf

# Use cached or actual file if they exists, Otherwise pass request to WordPress
location / {
  try_files $uri $uri/ /index.php$is_args$args;
}

location /redis-fetch {
    internal  ;
    set  $redis_key $args;
    redis_pass redis;
}
location /redis-store {
    internal  ;
    set_unescape_uri $key $arg_key ;
    redis2_query  set $key $echo_request_body;
    redis2_query expire $key 14400;
    redis2_pass redis;

}

location ~ \.php$ {
  set $key "nginx-cache:$scheme$request_method$host$request_uri";
  try_files $uri =404;

  srcache_fetch_skip $skip_cache;
  srcache_store_skip $skip_cache;

  srcache_response_cache_control off;

  set_escape_uri $escaped_key $key;

  srcache_fetch GET /redis-fetch $key;
  srcache_store PUT /redis-store key=$escaped_key;

  more_set_headers 'X-SRCache-Fetch-Status $srcache_fetch_status';
  more_set_headers 'X-SRCache-Store-Status $srcache_store_status';

  include fastcgi_params;
  fastcgi_pass {{upstream}};
}
