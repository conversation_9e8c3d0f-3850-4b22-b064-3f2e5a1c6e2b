# WEBP NGINX CONFIGURATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE

map $http_accept $webp_suffix_valid {
   default 1;
   "~*webp" 0;
}

map $realip_remote_addr $webp_suffix_cf {
    default 0;
    ************/22         1;
    ************/22         1;
    **********/22           1;
    **********/12           1;
    *************/18        1;
    **********/22           1;
    ************/18         1;
    ***********/15          1;
    **********/13           1;
    ************/20         1;
    ************/20         1;
    ************/20         1;
    *************/22        1;
    ************/17         1;
    ************/21         1;
    2400:cb00::/32          1;
    2405:8100::/32          1;
    2405:b500::/32          1;
    2606:4700::/32          1;
    2803:f800::/32          1;
    2a06:98c0::/29          1;
    2c0f:f248::/32          1;

}

map $webp_suffix_cf$webp_suffix_valid $webp_suffix {
    default "";
    00 ".webp";
}