#!/bin/sh
# script to update motd when a new WordOps release is available on Debian/Ubuntu
# the script is added in /etc/update-motd.d

safe_print() {
    cat "$1" | head -n 10 | tr -d '\000-\011\013\014\016-\037' | cut -c -80
}

# Ensure sane defaults
[ -n "$URL" ] || URL="https://api.github.com/repos/WordOps/WordOps/releases/latest"
[ -n "$WAIT" ] || WAIT=5
[ -n "$CACHE" ] || CACHE="/var/cache/motd-wo"

# Generate our temp files, clean up when done
NEWS=$(mktemp) || exit 1
ERR=$(mktemp) || exit 1
CLOUD=$(mktemp) || exit 1
trap "rm -f $NEWS $ERR $CLOUD" HUP INT QUIT ILL TRAP BUS TERM

if [ -n "$(command -v curl)" ]; then
    LATEST_RELEASE=$(curl -m 5 --retry 3 -sL "$URL" | jq -r '.tag_name' 2>&1)
fi
if [ -n "$(command -v wo)" ]; then
    CURRENT_RELEASE=$(wo -v 2>&1 | grep v | awk -F " " '{print $2}')
fi
if [ -n "$CURRENT_RELEASE" ] && [ -n "$LATEST_RELEASE" ]; then
    if [ "$CURRENT_RELEASE" != "$LATEST_RELEASE" ]; then
        # display message with motd-news on Ubuntu
        echo '*** A new WordOps release is available ***' >"$NEWS" 2>"$ERR"

        echo
        # At most, 10 lines of text, remove control characters, print at most 80 characters per line
        safe_print "$NEWS"
        # Try to update the cache
        safe_print "$NEWS" 2>/dev/null >$CACHE || true
    else
        # clean news
        echo '' >"$NEWS" 2>"$ERR"
        safe_print "$NEWS" 2>/dev/null >$CACHE || true
    fi
fi
