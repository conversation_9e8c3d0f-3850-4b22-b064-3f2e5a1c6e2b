##
# WordOps Settings
##


tcp_nopush on;
tcp_nodelay on;
types_hash_max_size 2048;

server_tokens off;
reset_timedout_connection on;
add_header X-Powered-By "WordOps {{ version }}";

# Limit Request
limit_req_status 403;
limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;

# Proxy Settings
# set_real_ip_from	proxy-server-ip;
# real_ip_header	X-Forwarded-For;

fastcgi_read_timeout 300;
client_max_body_size 100m;

##
# SSL Settings
##

ssl_session_cache shared:SSL:5m;
ssl_session_timeout 10m;
ssl_prefer_server_ciphers on;
ssl_ciphers "ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-CHACHA20-POLY1305:EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
ssl_protocols TLSv1.1 TLSv1.2;

##
# Basic Settings
##
server_names_hash_bucket_size 16384;
# server_name_in_redirect off;


##
# Logging Settings
##

access_log /var/log/nginx/access.log;
error_log /var/log/nginx/error.log;

# Log format Settings
log_format rt_cache '$remote_addr $upstream_response_time $upstream_cache_status [$time_local] '
'$host "$request" $status $body_bytes_sent '
'"$http_referer" "$http_user_agent" "$request_body" "$http3"';

##
# Gzip Settings
##

gzip on;
gzip_disable "msie6";

gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_buffers 16 8k;
gzip_http_version 1.1;
gzip_types
    application/atom+xml
    application/javascript
    application/json
    application/rss+xml
    application/vnd.ms-fontobject
    application/x-font-ttf
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/opentype
    image/svg+xml
    image/x-icon
    text/css
    text/plain
    text/x-component
    text/xml
    text/javascript;
