user www-data;
worker_processes auto;
worker_cpu_affinity auto;
worker_rlimit_nofile 100000;
pid /run/nginx.pid;

pcre_jit on;

events {
	multi_accept on;
	worker_connections 50000;
	accept_mutex on;
	use epoll;
}


http {
	##
	# WordOps Settings - WordOps {{release}}
	##

	keepalive_timeout 8;

	# Nginx AIO : See - https://www.nginx.com/blog/thread-pools-boost-performance-9x/
	# http://nginx.org/en/docs/http/ngx_http_core_module.html#aio
	aio threads;

	server_tokens off;
	reset_timedout_connection on;
	more_set_headers "X-Powered-By : WordOps";

	# Limit Request
	limit_req_status 403;
	limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
	limit_req_zone $binary_remote_addr zone=two:10m rate=10r/s;

	# Proxy Settings
	# set_real_ip_from	proxy-server-ip;
	# real_ip_header	X-Forwarded-For;

	fastcgi_read_timeout 300;
	client_max_body_size 100m;

	# ngx_vts_module
	vhost_traffic_status_zone;

	# tls dynamic records patch directive
	ssl_dyn_rec_enable on;

	##
	# SSL Settings
	##

	# Enable 0-RTT support for TLS 1.3
	proxy_set_header Early-Data $ssl_early_data;
	ssl_early_data on;

	# enable http/2
	http2 on;

	ssl_session_timeout 1d;
	ssl_session_cache shared:SSL:50m;
	ssl_session_tickets off;
	ssl_prefer_server_ciphers on;
	ssl_ciphers 'TLS13+AESGCM+AES256:TLS13+AESGCM+AES128:TLS13+CHACHA20:EECDH+AESGCM:EECDH+CHACHA20';
	ssl_protocols TLSv1.2 TLSv1.3;
	ssl_ecdh_curve X25519:P-521:P-384:P-256;

	# Common security headers
	more_set_headers "X-Frame-Options : SAMEORIGIN";
	more_set_headers "X-Content-Type-Options : nosniff";
	more_set_headers "Referrer-Policy : strict-origin-when-cross-origin";

	# oscp settings
	resolver ******* ******* ******* ******* valid=300s;
	resolver_timeout 10;
	ssl_stapling on;

	##
	# Basic Settings
	##
	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# Logging Settings
	##

	access_log off;
	error_log /var/log/nginx/error.log;

	# Log format Settings
	log_format rt_cache '$remote_addr $upstream_response_time $upstream_cache_status [$time_local] '
	'$host "$request" $status $body_bytes_sent '
	'"$http_referer" "$http_user_agent" "$server_protocol" "$http3"';

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;
}


#mail {
#	# See sample authentication script at:
#	# http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#	# auth_http localhost/auth.php;
#	# pop3_capabilities "TOP" "USER";
#	# imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#	server {
#		listen	 localhost:110;
#		protocol   pop3;
#		proxy	  on;
#	}
#
#	server {
#		listen	 localhost:143;
#		protocol   imap;
#		proxy	  on;
#	}
#}
