# NGINX Tweaks  - WordOps {{release}}
	# Enables the use of the O_DIRECT flag t can be useful for serving large files
    directio 4m;
	directio_alignment 512;
	large_client_header_buffers 8 64k;

	postpone_output 1460;
	proxy_buffers 8 32k;
	proxy_buffer_size 64k;

	sendfile on;
	sendfile_max_chunk 512k;

	tcp_nopush on;
	tcp_nodelay on;

	keepalive_requests 500;
	keepalive_disable msie6;

	lingering_time 20s;
	lingering_timeout 5s;

	open_file_cache max=50000 inactive=60s;
	open_file_cache_errors off;
	open_file_cache_min_uses 2;
	open_file_cache_valid 120s;
	open_log_file_cache max=10000 inactive=30s min_uses=2;

	ssl_dyn_rec_size_hi 4229;
	ssl_dyn_rec_size_lo 1369;
	ssl_dyn_rec_threshold 40;
	ssl_dyn_rec_timeout 1000;
