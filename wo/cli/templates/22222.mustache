# WordOps admin NGINX CONFIGURATION - WordOps {{release}}

server {

  listen {{port}} default_server ssl;

  access_log   /var/log/nginx/22222.access.log rt_cache;
  error_log    /var/log/nginx/22222.error.log;

  # Force HTTP to HTTPS
  error_page 497 =200 https://$host:{{port}}$request_uri;

  root {{webroot}}22222/htdocs;
  index index.php index.htm index.html;

  # Turn on directory listing
  autoindex on;

  # HTTP Authentication on port 22222
  include common/acl.conf;

  # nginx-vts-status
  location /vts_status {
    vhost_traffic_status_bypass_limit on;
    vhost_traffic_status_bypass_stats on;
    vhost_traffic_status_display;
    vhost_traffic_status_display_format html;
  }

  location / {
    try_files $uri $uri/ /index.php$is_args$args;
  }

  # Display menu at location /fpm/status/
  location =  /fpm/status/ {}

  location ~ /fpm/status/(.*) {
    try_files $uri =404;
    include fastcgi_params;
    fastcgi_param  SCRIPT_NAME  /status;
    fastcgi_pass $1;
  }

  location ~ \.php$ {
    try_files $uri =404;
    include fastcgi_params;
    fastcgi_pass multiphp;
  }

     location /netdata {
        return 301 /netdata/;
   }

   location ~ /netdata/(?<ndpath>.*) {
        proxy_redirect off;
        proxy_set_header Host $host;

        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_pass_request_headers on;
        proxy_set_header Connection "keep-alive";
        proxy_store off;
        proxy_pass http://netdata/$ndpath$is_args$args;

    }

    include {{webroot}}22222/conf/nginx/*.conf;

}