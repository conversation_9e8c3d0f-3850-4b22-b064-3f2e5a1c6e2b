# WPROCKET NGINX CONFIGURATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
# $cache_uri variable set in /etc/nginx/conf.d/map-wp.conf
# Use cached or actual file if they exists, Otherwise pass request to WordPress
location / {
    try_files /wp-content/cache/wp-rocket/$http_host/$cache_uri/index${mobile_prefix}${https_prefix}.html /wp-content/cache/wp-rocket/$http_host/$cache_uri/index${https_prefix}.html $uri $uri/ /index.php$is_args$args;
}
location ~ \.php$ {
    try_files $uri =404;
    include fastcgi_params;
    fastcgi_pass {{upstream}};
}
location ~ /wp-content/cache/wp-rocket/*\.html$ {
    etag on;
    gzip_static on;
    add_header Vary "Accept-Encoding, Cookie";
    access_log off;
    log_not_found off;
    expires 10h;
}
