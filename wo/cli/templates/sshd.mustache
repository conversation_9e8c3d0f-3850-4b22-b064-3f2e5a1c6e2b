# Use a custom port in the following range : 1024-65536
Port {{sshport}}

# Supported HostKey algorithms by order of preference.
HostKey /etc/ssh/ssh_host_ed25519_key
HostKey /etc/ssh/ssh_host_rsa_key
HostKey /etc/ssh/ssh_host_ecdsa_key

# Allow root access with ssh keys
PermitRootLogin without-password

# Allow ssh access to some users only
AllowUsers root ubuntu debian {{user}}

# allow ssh key Authentication
PubkeyAuthentication yes

# ssh keys path in ~/.ssh/authorized_keys
AuthorizedKeysFile      %h/.ssh/authorized_keys

# No password or empty passwords Authentication
PasswordAuthentication {{allowpass}}
PermitEmptyPasswords no

# No challenge response Authentication
ChallengeResponseAuthentication no

UsePAM yes
X11Forwarding yes

PrintMotd yes

# Allow client to pass locale environment variables
AcceptEnv LANG LC_*

# LogLevel VERBOSE logs user's key fingerprint on login. Needed to have a clear audit track of which key was using to log in.
LogLevel VERBOSE

# Log sftp level file access (read/write/etc.) that would not be easily logged otherwise.
Subsystem sftp /usr/lib/openssh/sftp-server -f AUTHPRIV -l INFO

# Host keys the client accepts - order here is honored by OpenSSH
HostKeyAlgorithms <EMAIL>,<EMAIL>,ssh-ed25519,ssh-rsa,<EMAIL>,<EMAIL>,<EMAIL>,ecdsa-sha2-nistp521,ecdsa-sha2-nistp384,ecdsa-sha2-nistp256

# use strong ciphers
KexAlgorithms <EMAIL>,ecdh-sha2-nistp521,ecdh-sha2-nistp384,ecdh-sha2-nistp256,diffie-hellman-group-exchange-sha256
Ciphers <EMAIL>,<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr
MACs <EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-512,hmac-sha2-256,<EMAIL>

# Use kernel sandbox mechanisms where possible in unprivileged processes
# Systrace on OpenBSD, Seccomp on Linux, seatbelt on MacOSX/Darwin, rlimit elsewhere.
UsePrivilegeSeparation sandbox