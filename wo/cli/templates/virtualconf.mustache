
server {

    {{#multisite}}
    # Uncomment the following line for domain mapping
    # listen 80 default_server;
    {{/multisite}}

    server_name {{site_name}} {{#multisite}}*{{/multisite}}{{^multisite}}www{{/multisite}}.{{site_name}};

    {{#multisite}}
    # Uncomment the following line for domain mapping
    #server_name_in_redirect off;
    {{/multisite}}

    access_log /var/log/nginx/{{site_name}}.access.log {{^wpredis}}{{^static}}rt_cache{{/static}}{{/wpredis}}{{#wpredis}}rt_cache_redis{{/wpredis}};
    error_log /var/log/nginx/{{site_name}}.error.log;

    {{#alias}}
    location / {
        return 301 https://{{alias_name}}$request_uri;
    }
    {{/alias}}
    {{#proxy}}
    add_header X-Proxy-Cache $upstream_cache_status;
    location / {
        proxy_pass http://{{host}}:{{port}};
        proxy_redirect      off;
        include proxy_params;
    }
    {{#alias}}
    # Security settings for better privacy
    # Deny hidden files
    location ~ /\.(?!well-known\/) {
        deny all;
    }
    # letsencrypt validation
    location /.well-known/acme-challenge/ {
        alias /var/www/html/.well-known/acme-challenge/;
        allow all;
        auth_basic off;
    }
    {{/alias}}
    {{/proxy}}

    {{^proxy}}
    {{^alias}}
    
    {{^subsite}}
    root {{webroot}}/htdocs;
    {{/subsite}}
    
    {{#subsite}}
    root {{subsiteof_webroot}}/htdocs;
    {{/subsite}}

    index {{^static}}index.php{{/static}} index.html index.htm;

    {{#static}}
    location / {
        try_files $uri $uri/ =404;
    }
    {{/static}}

    {{^static}}include {{#basic}}common/{{wo_php}}.conf;{{/basic}}{{#wpfc}}common/wpfc-{{wo_php}}.conf;{{/wpfc}}{{#wpsc}}common/wpsc-{{wo_php}}.conf;{{/wpsc}}{{#wpredis}}common/redis-{{wo_php}}.conf;{{/wpredis}}{{#wprocket}}common/wprocket-{{wo_php}}.conf;{{/wprocket}}{{#wpce}}common/wpce-{{wo_php}}.conf;{{/wpce}}
    {{#wpsubdir}}include common/wpsubdir.conf;{{/wpsubdir}}{{/static}}
    {{#wp}}include common/wpcommon-{{wo_php}}.conf;{{/wp}}
    include common/locations-wo.conf;{{/alias}}{{/proxy}}
    include {{webroot}}/conf/nginx/*.conf;

}
