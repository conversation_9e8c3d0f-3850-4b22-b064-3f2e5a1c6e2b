# NGINX CONFIGURATION FOR COMMON LOCATION - WordOps {{release}}
# DO NOT MODIFY, ALL CHANGES WILL BE LOST AFTER AN WordOps (wo) UPDATE
# Basic locations files
location = /favicon.ico {
    try_files /wp-content/uploads/fbrfg/favicon.ico $uri $uri/ /index.php?$args @empty_gif;
    access_log off;
    log_not_found off;
    expires max;
}
location @empty_gif {
    empty_gif;
}
# Cache static files
location ~* \.(ogg|ogv|svg|svgz|eot|otf|woff|woff2|ttf|m4a|mp4|ttf|rss|atom|jpe?g|gif|cur|heic|png|tiff|ico|webm|mp3|aac|tgz|doc|xls|exe|ppt|tar|mid|midi|wav|bmp|rtf|swf|webp|json|webmanifest|cast)$ {
    more_set_headers 'Access-Control-Allow-Origin : *';
    more_set_headers  "Cache-Control : public, no-transform";
    access_log off;
    log_not_found off;
    expires max;
}
# Cache css & js files
location ~* \.(?:css(\.map)?|js(\.map)?)$ {
    more_set_headers 'Access-Control-Allow-Origin : *';
    more_set_headers  "Cache-Control : public, no-transform";
    access_log off;
    log_not_found off;
    expires 1y;
}
# Security settings for better privacy
# Deny hidden files
location ~ /\.(?!well-known\/) {
    deny all;
}
# letsencrypt validation
location /.well-known/acme-challenge/ {
   alias /var/www/html/.well-known/acme-challenge/;
   allow all;
   auth_basic off;
}
# Private Prefetch Proxy
# https://developer.chrome.com/blog/private-prefetch-proxy/
location /.well-known/traffic-advice {
   types { } default_type "application/trafficadvice+json; charset=utf-8";
   return 200 "[{\n  \"user_agent\": \"prefetch-proxy\",\n  \"google_prefetch_proxy_eap\": {\n    \"fraction\": 1.0\n  }\n}]";
   allow all;
}
# Return 403 forbidden for readme.(txt|html) or license.(txt|html) or example.(txt|html) or other common git repository files
location ~*  "/(^$|readme|license|example|README|LEGALNOTICE|INSTALLATION|CHANGELOG)\.(txt|html|md)" {
    deny all;
}
# Deny backup extensions & log files and return 403 forbidden
location ~* "\.(old|orig|original|php#|php~|php_bak|save|swo|aspx?|tpl|sh|bash|bak?|cfg|cgi|dll|exe|git|hg|ini|jsp|log|mdb|out|sql|svn|swp|tar|rdf|gz|zip|bz2|7z|pem|asc|conf|dump)$" {
    deny all;
}
location ~* "/(=|\$&|_mm|(wp-)?config\.|cgi-|etc/passwd|muieblack)" {
    deny all;
}

# block base64_encoded content
location ~* "(base64_encode)(.*)(\()" {
    deny all;
}

# block javascript eval()
location ~* "(eval\()" {
    deny all;
}

# Additional security settings

location ~* "(127\.0\.0\.1)" {
    deny all;
}
location ~* "([a-z0-9]{2000})" {
    deny all;
}
location ~* "(javascript\:)(.*)(\;)" {
    deny all;
}
location ~* "(GLOBALS|REQUEST)(=|\[|%)" {
    deny all;
}
location ~* "(<|%3C).*script.*(>|%3)" {
    deny all;
}
location ~ "(\\|\.\.\.|\.\./|~|`|<|>|\|)" {
    deny all;
}
location ~* "(boot\.ini|etc/passwd|self/environ)" {
    deny all;
}
location ~* "(thumbs?(_editor|open)?|tim(thumb)?)\.php" {
    deny all;
}
location ~* "(\'|\")(.*)(drop|insert|md5|select|union)" {
    deny all;
}
location ~* "(https?|ftp|php):/" {
    deny all;
}
location ~* "(=\\\'|=\\%27|/\\\'/?)\." {
    deny all;
}
location ~ "(\{0\}|\(/\(|\.\.\.|\+\+\+|\\\"\\\")" {
    deny all;
}
location ~ "(~|`|<|>|:|;|%|\\|\s|\{|\}|\[|\]|\|)" {
    deny all;
}
