import os
import smtplib
from email import encoders
from email.mime.base import MIME<PERSON><PERSON>
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText
from email.utils import COMMASPACE, formatdate


def WOSendMail(send_from, send_to, subject, text, files, server="localhost",
               port=587, username='', password='', isTls=True):
    msg = MIMEMultipart()
    msg['From'] = send_from
    msg['To'] = send_to
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject

    msg.attach(MIMEText(text))

    for file in files:
        part = MIMEBase('application', "octet-stream")
        with open(file, 'rb') as f:
            part.set_payload(f.read())
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', 'attachment; filename="{0}"'
                        .format(os.path.basename(f)))
        msg.attach(part)

    smtp = smtplib.SMTP(server, port)
    if isTls:
        smtp.starttls()

    smtp.sendmail(send_from, send_to, msg.as_string())
    smtp.quit()
