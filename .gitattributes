# Auto detect text files and perform LF normalization
*          text=auto

# Documents
*.md       text eol=lf
*.tex      text diff=tex
*.adoc     text
*.textile  text
*.mustache text eol=lf
*.csv      text
*.tab      text
*.tsv      text
*.txt      text
*.sql      text

# Scripts
*.bash     text eol=lf
*.fish     text eol=lf
*.sh       text eol=lf
*.rc       text eol=lf

# Source files
# ============
*.pxd    text diff=python
*.py     text diff=python
*.py3    text diff=python
*.pyc    text diff=python
*.pyd    text diff=python
*.pyo    text diff=python
*.pyw    text diff=python
*.pyx    text diff=python
*.pyz    text diff=python


#
# Exclude files from exporting
#

.gitattributes export-ignore
.gitignore     export-ignore