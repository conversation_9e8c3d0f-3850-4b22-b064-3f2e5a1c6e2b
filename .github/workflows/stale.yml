name: 'Close stale issues and PRs'
permissions:
  contents: read
  issues: write
  pull-requests: write
on:
  schedule:
    - cron: '0 0 1 */6 *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v8
        with:
          stale-issue-message: 'This issue is stale because it has been open 45 days with no activity. Remove stale label or comment or this will be closed in 5 days.'
          stale-pr-message: 'This PR is stale because it has been open 90 days with no activity. Remove stale label or comment or this will be closed in 10 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 5 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 10 days with no activity.'
          days-before-issue-stale: 180
          days-before-pr-stale: 180
          days-before-issue-close: 30
          days-before-pr-close: 30
          operations-per-run: 100
