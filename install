#!/usr/bin/env bash
# -------------------------------------------------------------------------
# WordOps install and update script
# -------------------------------------------------------------------------
# Website:       https://wordops.net
# GitHub:        https://github.com/WordOps/WordOps
# Copyright (c) 2019-2025 - WordOps
# This script is licensed under M.I.T
# -------------------------------------------------------------------------
# wget -qO wo wops.cc && sudo -E bash wo
# -------------------------------------------------------------------------
# Version 3.22.0 - 2024-12-06
# -------------------------------------------------------------------------

# CONTENTS
# ---
# 1. VARIABLES AND DECLARATIONS
# 2. PREPARE FOR INSTALLATION
# 3. INSTALLATION
# 4.

###
# 1 - Set the CLI output colors
###

CSI='\033['
TPUT_RESET="${CSI}0m"
TPUT_FAIL="${CSI}1;31m"
TPUT_ECHO="${CSI}1;36m"
TPUT_OK="${CSI}1;32m"

wo_lib_echo() {

    echo -e "${TPUT_ECHO}${*}${TPUT_RESET}"
}

wo_lib_echo_info() {

    echo -e "$*"
}

wo_lib_echo_fail() {

    echo -e "${TPUT_FAIL}${*}${TPUT_RESET}"
}

###
# 1 - Capture errors
###

wo_lib_error() {
    echo -e "[ $(date) ] ${TPUT_FAIL}${*}${TPUT_RESET}"
    exit "$2"
}

###
# 1 - script argument parsing
###

while [ "$#" -gt 0 ]; do
    case "$1" in
    -b | --branch)
        wo_branch="$2"
        shift
        ;;
    --force)
        wo_force_install="y"
        ;;
    --travis)
        wo_travis="y"
        wo_force_install="y"
        ;;
    --mainline | --beta)
        wo_branch="mainline"
        ;;
    -s | --silent)
        wo_force_install="y"
        ;;
    --purge | --uninstall)
        wo_purge="y"
        ;;
    *) # positional args
        ;;
    esac
    shift
done

###
# 1 - Check whether the installation is called with elevated rights
###
if [[ $EUID -ne 0 ]]; then
    wo_lib_echo_fail "Sudo privilege required..."
    wo_lib_echo_fail "Use: wget -qO wo wops.cc && sudo bash wo  "
    exit 100
fi

export DEBIAN_FRONTEND=noninteractive
unset LANG
export LANG='en_US.UTF-8'
export LC_ALL='C.UTF-8'

###
# 1- Main functions
###

# check if a command exist
command_exists() {
    command -v "$@" >/dev/null 2>&1
}

# run functions and exit on failure
_run() {
    if [ -n "$2" ]; then
        echo -ne "${TPUT_ECHO}${2}${TPUT_RESET}\t"
    fi
    if ! { "$1" >>"$wo_install_log" 2>&1; }; then
        if [ -n "$2" ]; then
            echo -e "${TPUT_FAIL}[KO]${TPUT_RESET}"
        fi
    else
        if [ -n "$2" ]; then
            echo -e "[${TPUT_OK}OK${TPUT_RESET}]"
        fi
    fi

}

_curl() {
    curl -m 10 --retry 3 -sL "$@"
}

wo_init_variables() {
    if [ -z "$wo_branch" ]; then
        if [ "$wo_travis" = "y" ]; then
            wo_branch=updating-configuration
        else
            wo_branch=master
        fi
    fi
    readonly wo_install_log=/var/log/wo/install.log
    readonly TIME_FORMAT='%d-%b-%Y-%H%M%S'
    TIME=$(date +"$TIME_FORMAT")
    readonly TIME
    readonly NGINX_BACKUP_FILE="/var/lib/wo-backup/nginx-backup.$TIME.tar.zst"
    readonly EE_BACKUP_FILE="/var/lib/wo-backup/ee-backup.$TIME.tar.zst"
    readonly WO_BACKUP_FILE="/var/lib/wo-backup/wo-backup.$TIME.tar.zst"

    if [ -x /usr/local/bin/ee ]; then
        ee_migration=1
    elif [ -x /usr/local/bin/wo ]; then
        wo_upgrade=1
    fi
}

###
# 1 - Checking linux distro
###

wo_check_distro() {
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)
    local wo_distro_version
    wo_distro_version=$(lsb_release -sc)
    if [ -z "$wo_force_install" ]; then
        if [ "$wo_linux_distro" != "Ubuntu" ] && [ "$wo_linux_distro" != "Debian" ] && [ "$wo_linux_distro" != "Raspbian" ]; then
            wo_lib_echo_fail "WordOps (wo) only supports Ubuntu, Debian & Raspbian at the moment."
            wo_lib_echo_fail "You can bypass this warning by adding the flag --force to the install command"
            wo_lib_echo_fail "Feel free to open a pull-request if you want to add support for another Linux distributions"
            exit 100
        else
            check_wo_linux_distro=$(lsb_release -sc | grep -E "buster|focal|jammy|bullseye|bookworm|noble")
            if [ -z "$check_wo_linux_distro" ]; then
                wo_lib_echo_fail "WordOps (wo) only supports Ubuntu 20.04/22.04/24.04 LTS, Debian 10.x/11.x/12.x and Raspbian 10x./11.x/12.x \n
                You can bypass this warning by adding the flag --force to the install command"
                exit 100
            fi
        fi
    fi

}

###
# 1 - To prevent errors or unexpected behaviour, create the log and ACL it
###
wo_dir_init() {
    local wo_log_dir=/var/log/wo
    local wo_backup_dir=/var/lib/wo-backup
    local wo_tmp_dir=/var/lib/wo/tmp
    if [ ! -d "$wo_log_dir" ] || [ ! -d "$wo_backup_dir" ] || [ ! -d "$wo_tmp_dir" ]; then

        mkdir -p "$wo_backup_dir" "$wo_log_dir" "$wo_tmp_dir"

        # create wordops log files
        touch /var/log/wo/{wordops.log,install.log}

        chmod -R 750 "$wo_log_dir" "$wo_backup_dir" "$wo_tmp_dir"
        chown -R root:adm "$wo_log_dir"
    fi

}

###
# 2 - Setup the dependencies for installation
####

wo_install_dep() {
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)
    wo_python_venv=$(apt-cache policy python3.12-venv 2>&1 | grep -q Installed)
    if [ "$wo_linux_distro" == "Ubuntu" ]; then
        # install dependencies
        add-apt-repository ppa:git-core/ppa -y
        apt-get --option=Dpkg::options::=--force-confmiss --option=Dpkg::options::=--force-confold --assume-yes install \
            build-essential curl gzip python3-pip python3-apt python3-venv gcc python3-dev sqlite3 git tar software-properties-common pigz \
            gnupg2 cron ccze rsync apt-transport-https tree haveged ufw unattended-upgrades tzdata ntp zstd idn \
            python3-distutils-extra libapt-pkg-dev bash-completion >/dev/null 2>&1
        if $wo_python_venv; then
            apt install -yy python3.12-venv >/dev/null 2>&1
        fi

    else
        # install dependencies
        apt-get --option=Dpkg::options::=--force-confmiss --option=Dpkg::options::=--force-confold --assume-yes install \
            build-essential curl gzip dirmngr sudo python3-pip python3-apt python3-venv gcc python3-dev ca-certificates sqlite3 git tar \
            software-properties-common pigz apt-transport-https gnupg2 cron ccze rsync tree haveged ufw unattended-upgrades tzdata ntp zstd idn \
            python3-distutils-extra libapt-pkg-dev bash-completion >/dev/null 2>&1
        # add php repository gpg key
        curl -sSLo /tmp/debsuryorg-archive-keyring.deb https://packages.sury.org/debsuryorg-archive-keyring.deb
        dpkg -i /tmp/debsuryorg-archive-keyring.deb && rm -f /tmp/debsuryorg-archive-keyring.deb
    fi
    locale-gen en
    # enable unattended upgades
    if [ ! -f /etc/apt/apt.conf.d/20auto-upgrades ]; then
        cp -f /usr/share/unattended-upgrades/20auto-upgrades /etc/apt/apt.conf.d/20auto-upgrades
    fi
}

wo_download_gpg_keys() {
    local wo_distro_version
    wo_distro_version=$(lsb_release -rs | grep -oE '[0-9]+')
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)

    # create directories
    mkdir -p /usr/share/keyrings /etc/apt/keyrings

    # redis gpg key
    curl -fsSL https://packages.redis.io/gpg | gpg --dearmor | tee /usr/share/keyrings/redis-archive-keyring.gpg >/dev/null 2>&1

    # mariadb
    curl -o /etc/apt/keyrings/mariadb-keyring.pgp 'https://mariadb.org/mariadb_release_signing_key.pgp'

    # nginx
    if [ "$wo_linux_distro" == "Debian" ]; then
        curl -fsSL "https://download.opensuse.org/repositories/home:virtubox:WordOps/Debian_$wo_distro_version/Release.key" | gpg --dearmor | tee /usr/share/keyrings/wordops-archive-keyring.gpg >/dev/null 2>&1
    fi
    if [ "$wo_linux_distro" == "Raspbian" ]; then
        curl -fsSL "https://download.opensuse.org/repositories/home:virtubox:WordOps/Raspbian_$wo_distro_version/Release.key" | gpg --dearmor | tee /usr/share/keyrings/wordops-archive-keyring.gpg >/dev/null 2>&1
    fi
}

wo_update_repo() {
    local wo_linux_codename
    wo_linux_codename=$(lsb_release -sc)
    if [ -f /etc/apt/sources.list.d/wo-repo.list ]; then
        # properly define sury repository
        if grep -q sury /etc/apt/sources.list.d/wo-repo.list; then
            echo "deb [signed-by=/usr/share/keyrings/deb.sury.org-php.gpg] https://packages.sury.org/php/ $wo_linux_codename main" >/etc/apt/sources.list.d/php.list
        fi
        # properly define mariadb repository
        repo_file="/etc/apt/sources.list.d/wo-repo.list"
        if grep -q mariadb /etc/apt/sources.list.d/wo-repo.list; then
            repo_number=$(grep -c "mariadb" "$repo_file")
            if [ "$repo_number" -gt 1 ]; then
                get_urls=$(grep "mariadb" "$repo_file" | awk '{print $3}')
                mariadb_repo=$(echo "$get_urls" | sort -t '/' -k 6,6 -V | tail -n 1)
            else
                mariadb_repo=$(grep mariadb /etc/apt/sources.list.d/wo-repo.list | awk '{print $3}')
            fi
            echo "deb [signed-by=/etc/apt/keyrings/mariadb-keyring.pgp] $mariadb_repo $wo_linux_codename main" >/etc/apt/sources.list.d/mariadb.list
        fi
        # properly define redis repository
        if grep -q redis /etc/apt/sources.list.d/wo-repo.list; then
            echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $wo_linux_codename main" >/etc/apt/sources.list.d/redis.list
        fi
        # properly define WordOps nginx repository
        if grep -q WordOps /etc/apt/sources.list.d/wo-repo.list; then
            wo_repo=$(grep WordOps /etc/apt/sources.list.d/wo-repo.list | awk -F\  '{ print $2 }')
            echo "deb [signed-by=/usr/share/keyrings/wordops-archive-keyring.gpg] $wo_repo /" >/etc/apt/sources.list.d/wordops.list
        fi
        # cleanup wo-repo.list
        if grep -Eqv "WordOps|mariadb|sury|redis" /etc/apt/sources.list.d/wo-repo.list; then
            rm -f /etc/apt/sources.list.d/wo-repo.list
        else
            clean_wo_repo=$(grep -Ev "WordOps|mariadb|sury|redis" /etc/apt/sources.list.d/wo-repo.list)
            echo "$clean_wo_repo" >/etc/apt/sources.list.d/wo-repo.list
        fi
    fi
}

wo_timesync() {
    # set default ntp pools
    if [ "$wo_distro_codename" != "bookworm" ] && [ "$wo_distro_codename" != "noble" ]; then
        if [ -f /etc/systemd/timesyncd.conf ]; then
            if ! grep -q "time.cloudflare.com" /etc/systemd/timesyncd.conf; then
                sed -e 's/^#NTP=/NTP=time.cloudflare.com 0.ubuntu.pool.ntp.org 1.ubuntu.pool.ntp.org 2.ubuntu.pool.ntp.org 3.ubuntu.pool.ntp.org/' -i /etc/systemd/timesyncd.conf
                # enable ntp
                timedatectl set-ntp 1
            fi
        fi
    fi
}

###
# 3 - Create/migrate the essentials
###
wo_sync_db() {
    ###
    # Switching from EE -> WO
    ###
    if [ ! -f /var/lib/wo/dbase.db ]; then

        if [ -f /var/lib/ee/ee.db ]; then

            # Make a backup of the EasyEngine database
            cp /var/lib/ee/ee.db /var/lib/wo/dbase-ee.db

            # Copy ee database
            cp /var/lib/ee/ee.db /var/lib/wo/dbase.db
        else
            if [ -d /etc/nginx/sites-available ] && [ -d /var/www ]; then

                # Create an empty database for WordOps
                echo "CREATE TABLE sites (
           id INTEGER PRIMARY KEY     AUTOINCREMENT,
           sitename UNIQUE,
           site_type CHAR,
           cache_type CHAR,
           site_path  CHAR,
           created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
           is_enabled INT,
           is_ssl INT,
           storage_fs CHAR,
           storage_db CHAR,
           db_name VARCHAR,
           db_user VARCHAR,
           db_password VARCHAR,
           db_host VARCHAR,
           is_hhvm INT INT DEFAULT '0',
           php_version VARCHAR
            );" | sqlite3 /var/lib/wo/dbase.db

                # Check site is enable/live or disable
                AV_SITES="$(basename -a /etc/nginx/sites-available/* | grep -v default)"
                for site in $AV_SITES; do
                    if [ -h "/etc/nginx/sites-enabled/$site" ]; then
                        wo_site_status='1'
                    else
                        wo_site_status='0'
                    fi

                    # Acquire information about the current nginx configuration

                    wo_site_current_type=$(grep "common/" "/etc/nginx/sites-available/$site" | awk -F "/" '{print $2}')

                    if echo "$wo_site_current_type" | grep -q "redis"; then
                        wo_site_current_cache="wpredis"
                    elif echo "$wo_site_current_type" | grep -q wpsc; then
                        wo_site_current_cache="wpsc"
                    elif echo "$wo_site_current_type" | grep -q wpfc; then
                        wo_site_current_cache="wpfc"
                    else
                        wo_site_current_cache="basic"
                    fi

                    if echo "$wo_site_current_type" | grep -q wp; then
                        if echo "$wo_site_current_type" | grep -q wpsubdir; then
                            wo_site_current="wpsubdir"
                        elif echo "$wo_site_current_type" | grep -q wpsudomain; then
                            wo_site_current="wpsubdomain"
                        else
                            wo_site_current="wp"
                        fi
                    else
                        if echo "$wo_site_current_type" | grep -q location; then
                            wo_site_current="proxy"
                        elif echo "$wo_site_current_type" | grep -q php; then
                            wo_site_current="html"
                        else
                            if [ -f "/var/www/${site}/ee-config.php" ] || [ -f "/var/www/${site}/wo-config.php" ]; then
                                wo_site_current="mysql"
                            else
                                wo_site_current="php"
                            fi
                        fi
                    fi

                    wo_webroot="/var/www/$site"

                    # Import the configuration into the WordOps SQLite database
                    echo "INSERT INTO sites (sitename, site_type, cache_type, site_path, is_enabled, is_ssl, storage_fs, storage_db)
            VALUES (\"$site\", \"$wo_site_current\", \"$wo_site_current_cache\", \"$wo_webroot\", \"$wo_site_status\", 0, 'ext4', 'mysql');" | sqlite3 /var/lib/wo/dbase.db

                done
            fi
        fi

        #        echo "UPDATE sites SET php_version = REPLACE(php_version, '5.6', '7.2');" | sqlite3 /var/lib/wo/dbase.db
        #        echo "UPDATE sites SET php_version = REPLACE(php_version, '7.0', '7.3');" | sqlite3 /var/lib/wo/dbase.db
    fi
}

# Once again, set the proper ACL on the WordOps configuration directory
secure_wo_db() {

    # The owner is root
    chown -R root:root /var/lib/wo
    # Only allow access by root, block others
    chmod -R 600 /var/lib/wo

}

# Update the WP-CLI version
wo_update_wp_cli() {
    WP_CLI_PATH=$(command -v wp)
    if [ -n "$WP_CLI_PATH" ]; then
        rm -rf "$WP_CLI_PATH"
    fi
    # Update WP-CLI to the most recent version
    wget -qO /usr/local/bin/wp https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
    chmod +x /usr/local/bin/wp
    [ ! -h /usr/bin/wp ] && {
        ln -s /usr/local/bin/wp /usr/bin/
    }
    [ -d /etc/bash_completion.d ] && {
        wget -qO /etc/bash_completion.d/wp-completion.bash https://raw.githubusercontent.com/wp-cli/wp-cli/master/utils/wp-completion.bash
    }

}

wo_install_acme_sh() {
    # check if acme.sh is already installed
    if [ ! -x /etc/letsencrypt/acme.sh ]; then

        # clone the git repository
        if [ -d /opt/acme.sh/.git ]; then
            git -C /opt/acme.sh pull origin master
        else
            git clone --depth=50 https://github.com/Neilpang/acme.sh.git /opt/acme.sh -q
        fi
        cd /opt/acme.sh || exit 1
        # create conf directories
        mkdir -p /etc/letsencrypt/{config,live,renewal}
        # install acme.sh
        ./acme.sh --install \
            --home /etc/letsencrypt \
            --config-home /etc/letsencrypt/config \
            --cert-home /etc/letsencrypt/renewal
    fi

    if [ -x "$HOME/.acme.sh/acme.sh" ]; then
        # backup acme.sh folder
        /bin/tar -I zstd -cf /var/lib/wo-backup/acme.sh.tar.zst "$HOME/.acme.sh"
        # rsync previous certificates to new acme.sh location
        /usr/bin/rsync -rltgoDpz --exclude="account.conf" \
            --exclude="acme.sh" \
            --exclude="acme.sh.env" \
            --exclude="deploy" \
            --exclude="dnsapi" \
            --exclude="http.header" \
            --exclude="ca" \
            "$HOME/.acme.sh/" \
            /etc/letsencrypt/renewal/
        # remove previous acme.sh folder
        rm -rf "$HOME/.acme.sh"
        mkdir "$HOME/.acme.sh"
        touch "$HOME/.acme.sh/acme.sh.env"
        # removing previous cronjob
    fi
    # Let's Encrypt .well-known folder setup
    if [ ! -d /var/www/html/.well-known/acme-challenge ]; then
        mkdir -p /var/www/html/.well-known/acme-challenge
    fi
    chown -R www-data:www-data /var/www/html /var/www/html/.well-known
    chmod 750 /var/www/html /var/www/html/.well-known
    if [ -x /etc/letsencrypt/acme.sh ]; then
        export LE_WORKING_DIR="/etc/letsencrypt"
        export LE_CONFIG_HOME="/etc/letsencrypt/config"
        /etc/letsencrypt/acme.sh --config-home '/etc/letsencrypt/config' --upgrade --auto-upgrade
        /etc/letsencrypt/acme.sh --config-home '/etc/letsencrypt/config' --uninstall-cronjob
        /etc/letsencrypt/acme.sh --config-home '/etc/letsencrypt/config' --install-cronjob
        /etc/letsencrypt/acme.sh --set-default-ca --server letsencrypt
    fi

}

# WordOps install
wo_install() {
    local python_ver
    python_ver=$(python3 -c "import sys; print(sys.version_info[1])")
    local wo_distro_codename
    wo_distro_codename=$(lsb_release -sc)
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)
    if [ -d /usr/local/lib/python3."$python_ver"/dist-packages ]; then
        cd /usr/local/lib/python3."$python_ver"/dist-packages || exit 1
    fi
    if [ "$wo_distro_codename" != "bookworm" ]; then
        pip uninstall -yq wo wordops ee
    fi

    cd || exit 1
    if [ -d /opt/wo/lib/python3."$python_ver"/site-packages/apt ]; then
        source /opt/wo/bin/activate
        /opt/wo/bin/pip uninstall -yq setuptools
        /opt/wo/bin/pip install setuptools==80.0.1
        /opt/wo/bin/pip install -U pip wheel distro
        /opt/wo/bin/pip uninstall psutil -y
        /opt/wo/bin/pip uninstall SQLAlchemy -y
    else
        rm -rf /opt/wo
        python3 -m venv /opt/wo
        source /opt/wo/bin/activate
        /opt/wo/bin/pip uninstall -yq setuptools
        /opt/wo/bin/pip install setuptools==80.0.1
        /opt/wo/bin/pip install -U pip wheel
        /opt/wo/bin/pip install -U distro
        if [ "$wo_linux_distro" = "Debian" ] || [ "$wo_linux_distro" = "Raspbian" ]; then
            if [ "$wo_distro_codename" = "stretch" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.4.y#egg=python-apt
            elif [ "$wo_distro_codename" = "bullseye" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.2.1#egg=python-apt
            elif [ "$wo_distro_codename" = "bookworm" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.5.3#egg=python-apt
            else
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.8.4.y#egg=python-apt
            fi
        else
            if [ "$wo_distro_codename" = "focal" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.0.0#egg=python-apt
            elif [ "$wo_distro_codename" = "bionic" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.6.y#egg=python-apt
            elif [ "$wo_distro_codename" = "jammy" ]; then
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.2.1#egg=python-apt
            else
                /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.8.y#egg=python-apt
            fi
        fi
    fi

    if [ "$wo_branch" = "master" ]; then
        /opt/wo/bin/pip install -U wordops --upgrade-strategy=eager
    else
        /opt/wo/bin/pip install -I "git+https://github.com/WordOps/WordOps.git@$wo_branch#egg=wordops" --upgrade-strategy=eager
    fi
    local python_venv
    python_venv=$(python3 -c "import sys; print(sys.version_info[1])")
    cp -rf /opt/wo/lib/python3."$python_venv"/site-packages/usr/* /usr/
    cp -rn /opt/wo/lib/python3."$python_venv"/site-packages/etc/* /etc/
    cp -f /opt/wo/lib/python3."$python_venv"/site-packages/etc/bash_completion.d/wo_auto.rc /etc/bash_completion.d/wo_auto.rc
    rm -rf /usr/local/bin/wo /usr/local/lib/python3."$python_venv"/dist-packages/{etc,usr}
    ln -s /opt/wo/bin/wo /usr/local/bin/
    deactivate
    if [ ! -d /opt/wo/lib/python3."$python_ver"/site-packages/apt ]; then
        python3 -m venv --system-site-packages /opt/wo
    fi
}

# Clone Github repository if it doesn't exist
wo_travis_install() {
    local python_ver
    python_ver=$(python3 -c "import sys; print(sys.version_info[1])")
    local wo_distro_codename
    wo_distro_codename=$(lsb_release -sc)
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)
    if [ -d ./dist ]; then
        rm -rf dist
    fi
    if [ -f ./setup.py ]; then
        if [ -d /opt/wo/lib/python3."$python_ver"/site-packages/apt ]; then
            source /opt/wo/bin/activate
            /opt/wo/bin/pip uninstall -yq setuptools
            /opt/wo/bin/pip install setuptools==80.0.1
            /opt/wo/bin/pip install -U pip wheel
            /opt/wo/bin/pip uninstall psutil -y
            /opt/wo/bin/pip uninstall SQLAlchemy -y
        else
            rm -rf /opt/wo
            python3 -m venv /opt/wo
            source /opt/wo/bin/activate
            /opt/wo/bin/pip uninstall -yq setuptools
            /opt/wo/bin/pip install setuptools==80.0.1
            /opt/wo/bin/pip install -U pip wheel
            if [ "$wo_linux_distro" = "Debian" ] || [ "$wo_linux_distro" = "Raspbian" ]; then
                if [ "$wo_distro_codename" = "stretch" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.4.y#egg=python-apt
                elif [ "$wo_distro_codename" = "bullseye" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.2.1#egg=python-apt
                elif [ "$wo_distro_codename" = "bookworm" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.5.3#egg=python-apt
                else
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.8.4.y#egg=python-apt
                fi
            else
                if [ "$wo_distro_codename" = "focal" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.0.0#egg=python-apt
                elif [ "$wo_distro_codename" = "bionic" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@1.6.y#egg=python-apt
                elif [ "$wo_distro_codename" = "jammy" ]; then
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.2.1#egg=python-apt
                else
                    /opt/wo/bin/pip install git+https://salsa.debian.org/apt-team/python-apt.git@2.8.y#egg=python-apt
                fi
            fi
        fi
        /opt/wo/bin/pip install -U . --upgrade-strategy=eager
    else
        /opt/wo/bin/pip install -U "git+https://github.com/WordOps/WordOps.git@$wo_branch#egg=wordops" --upgrade-strategy=eager
    fi
    cp -rf /opt/wo/lib/python3."$python_ver"/site-packages/usr/* /usr/
    cp -rn /opt/wo/lib/python3."$python_ver"/site-packages/etc/* /etc/
    cp -f /opt/wo/lib/python3."$python_ver"/site-packages/etc/bash_completion.d/wo_auto.rc /etc/bash_completion.d/wo_auto.rc
    rm -f /usr/local/bin/wo
    ln -s /opt/wo/bin/wo /usr/local/bin/
    deactivate
}

wo_upgrade_nginx() {
    local wo_linux_distro
    wo_linux_distro=$(lsb_release -is)
    local wo_distro_version
    wo_distro_version=$(lsb_release -sc)

    if [ -d /var/lib/wo-backup/nginx ]; then
        /bin/tar -I zstd "$NGINX_BACKUP_FILE" /var/lib/wo-backup/nginx
        rm -rf /var/lib/wo-backup/nginx
    fi
    # backup nginx conf
    if [ -d /etc/nginx ]; then
        /usr/bin/rsync -a --noatime /etc/nginx/ /var/lib/wo-backup/nginx/
    fi
    if [ -d /etc/php ]; then
        /usr/bin/rsync -a --noatime /etc/php/ /var/lib/wo-backup/php/
    fi
    # chec if the package nginx-ee is installed
    CHECK_NGINX_EE=$(dpkg --list | grep nginx-ee)
    CHECK_PHP72=$(command -v php-fpm7.2)

    # install new nginx package
    if {
        if [ -n "$CHECK_NGINX_EE" ]; then
            if [ -x /usr/local/bin/wo ]; then
                [ -f /etc/apt/preferences.d/nginx-block ] && { mv /etc/apt/preferences.d/nginx-block /var/lib/wo/tmp/nginx-block; }
                # stop nginx
                service nginx stop
                # remove previous package
                apt-mark unhold nginx-ee nginx-common nginx-custom
                apt-get autoremove nginx-ee nginx-common nginx-custom --allow-change-held-packages --purge -qq
                # remove previous php-fpm pool configuration
                if [ -n "$CHECK_PHP72" ]; then
                    apt-get purge php7.2-fpm -y -qq
                    rm -f /etc/php/7.2/fpm/pool.d/{www.conf,www-two.conf,debug.conf}
                fi
                if [ -d /etc/nginx ]; then
                    rm -rf /etc/nginx
                fi
                /usr/local/bin/wo stack install --nginx --php
                rm -f /etc/nginx/common/acl.conf /etc/nginx/htpasswd-wo
                /usr/bin/rsync -au --noatime /var/lib/wo-backup/nginx/ /etc/nginx/
                /usr/local/bin/wo stack upgrade --nginx --force
            fi
        fi
    }; then
        # restore sites and configuration
        [ -f /etc/nginx/htpasswd-ee ] && { cp -f /etc/nginx/htpasswd-ee /etc/nginx/htpasswd-wo; }
        sed -i "s/locations.conf/locations-wo.conf/" /etc/nginx/sites-available/*
        sed -i "s/locations-php7.conf/locations-wo.conf/" /etc/nginx/sites-available/*
        sed -i "s/locations-php71.conf/locations-wo.conf/" /etc/nginx/sites-available/*
        sed -i "s/locations-php72.conf/locations-wo.conf/" /etc/nginx/sites-available/*
        sed -i "s/locations-php73.conf/locations-wo.conf/" /etc/nginx/sites-available/*
        sed -i "s/htpasswd-ee/htpasswd-wo/" /etc/nginx/common/acl.conf
        sed -i 's/ssl on;/#ssl on;/' /var/www/*/conf/nginx/ssl.conf
    fi

    # update redis.conf headers
    if [ -f /etc/nginx/common/redis.conf ]; then
        sed -i "s/X-Cache /X-SRCache-Fetch-Status /g" /etc/nginx/common/redis.conf
        sed -i "s/X-Cache-2 /X-SRCache-Store-Status /g" /etc/nginx/common/redis.conf

    fi

    VERIFY_NGINX_CONFIG=$(nginx -t 2>&1 | grep failed)
    # check if nginx -t do not return errors
    if [ -z "$VERIFY_NGINX_CONFIG" ]; then
        systemctl stop nginx
        systemctl start nginx
    else
        VERIFY_NGINX_BUCKET=$(nginx -t 2>&1 | grep "server_names_hash_bucket_size")
        if [ -n "$VERIFY_NGINX_BUCKET" ]; then
            sed -i "s/# server_names_hash_bucket_size 64;/server_names_hash_bucket_size 128;/g" /etc/nginx/nginx.conf
        fi
        systemctl stop nginx
        systemctl start nginx
    fi
    [ -f /var/lib/wo/tmp/nginx-block ] && { mv /var/lib/wo/tmp/nginx-block /etc/apt/preferences.d/nginx-block; }
    return 0
}

wo_update_latest() {

    # Move ~/.my.cnf to /etc/mysql/conf.d/my.cnf
    if [ -d /etc/mysql ]; then
        if [ ! -f /etc/mysql/conf.d/my.cnf ]; then
            # create conf.d folder if not exist
            [ ! -d /etc/mysql/conf.d ] && {
                mkdir -p /etc/mysql/conf.d
                chmod 755 /etc/mysql/conf.d
            }
            if [ -f /root/.my.cnf ]; then
                cp -f /root/.my.cnf /etc/mysql/conf.d/my.cnf
                chmod 600 /etc/mysql/conf.d/my.cnf
            elif [ -f "$HOME/.my.cnf" ]; then
                cp -f "$HOME/.my.cnf" /etc/mysql/conf.d/my.cnf
                chmod 600 /etc/mysql/conf.d/my.cnf
            fi
        fi
    fi
}

wo_backup_ee() {
    local BACKUP_EE=""
    [ -d /etc/nginx ] && { BACKUP_EE="$BACKUP_EE /etc/nginx"; }
    [ -d /etc/letsencrypt ] && { BACKUP_EE="$BACKUP_EE /etc/letsencrypt"; }
    /bin/tar -I zstd -cf "$EE_BACKUP_FILE" /usr/local/bin/ee /usr/lib/ee/templates /etc/ee /var/lib/ee "$BACKUP_EE"
    return 0
}

wo_backup_wo() {
    if [ -d /etc/nginx ] && [ -d /etc/wo ] && [ -d /var/lib/wo ]; then
        /bin/tar -I zstd -cf "$WO_BACKUP_FILE" /etc/nginx /etc/wo /var/lib/wo
    fi
    return 0
}

wo_clean_ee() {
    local python_ver
    python_ver=$(python3 -c "import sys; print(sys.version_info[1])")
    rm -rf /usr/local/bin/ee /etc/bash_completion.d/ee_auto.rc /usr/lib/ee/templates /usr/local/lib/python3."$python_ver"/dist-packages/ee-*.egg /etc/ee /var/lib/ee
    return 0
}

wo_remove_ee_cron() {

    crontab -l | sed '/ee site update --le=renew --all 2> \/dev\/null/d' | crontab -

}

wo_domain_suffix() {
    _curl https://raw.githubusercontent.com/publicsuffix/list/master/public_suffix_list.dat | sed '/^\/\//d' | sed '/^$/d' | sed 's/^\s+//g' >/var/lib/wo/public_suffix_list.dat
}

wo_clean() {
    local python_ver
    python_ver=$(python3 -c "import sys; print(sys.version_info[1])")
    rm -rf /usr/local/lib/python3."$python_ver"/dist-packages/{wo-*.egg,cement-*.egg,wordops-*.egg}
}

wo_uninstall() {
    local python_ver
    python_ver=$(python3 -c "import sys; print(sys.version_info[1])")
    if { /opt/wo/bin/pip list | grep -q "wordops" >/dev/null 2>&1; }; then
        /opt/wo/bin/pip uninstall wordops -y
    fi
    rm -rf /usr/local/lib/python3."$python_ver"/dist-packages/{pystache-*,cement-2.*,wo-*,wordops-*} /usr/local/bin/wo /etc/bash_completion.d/wo_auto.rc /var/lib/wo /etc/wo /usr/lib/wo/templates /opt/wo
}

wo_clean_repo() {
    # remove old EasyEngine Nginx repository
    if [ -f /etc/apt/sources.list.d/ee-repo.list ]; then
        cp -f /etc/apt/sources.list.d/ee-repo.list /etc/apt/sources.list.d/ee-repo.list.save
        grep -v "/home:/rtCamp:/EasyEngine" /etc/apt/sources.list.d/ee-repo.list.save >/etc/apt/sources.list.d/ee-repo.list
    fi
    if [ -f /etc/apt/sources.list.d/wo-repo.list ]; then
        local wo_linux_distro
        wo_linux_distro=$(lsb_release -is)
        cp -f /etc/apt/sources.list.d/wo-repo.list /etc/apt/sources.list.d/wo-repo.list.save
        if [ "$wo_linux_distro" = "Ubuntu" ]; then
            grep -v "opensuse" /etc/apt/sources.list.d/wo-repo.list.save >/etc/apt/sources.list.d/wo-repo.list
        else
            grep -v "/home:/rtCamp:/EasyEngine" /etc/apt/sources.list.d/wo-repo.list.save >/etc/apt/sources.list.d/wo-repo.list
        fi
    fi
}

wo_woconf() {
    if [ -f /etc/wo/wo.conf ]; then
        if grep -q "log.logging" /etc/wo/wo.conf; then
            sed -i "s/log.logging/log.colorlog/g" /etc/wo/wo.conf
        fi
        if ! grep -q "letsencrypt" /etc/wo/wo.conf; then
            echo -e '\n[letsencrypt]\n\nkeylength = "ec-384"' >>/etc/wo/wo.conf
        fi
        if ! grep -q "php" /etc/wo/wo.conf; then
            echo -e '\n[php]\n\nversion = 8.2' >>/etc/wo/wo.conf
        fi
    fi
}

wo_fix_kernel() {
    if [ -f /opt/wo-kernel.sh ]; then
        chmod +x /opt/wo-kernel.sh
    fi
}

wo_init() {

    ###
    # 1- Update the apt sewers with fresh info
    ###

    if [ -z "$wo_travis" ]; then
        if ! {
            apt-get update --allow-releaseinfo-change -qq >/dev/null 2>&1
        }; then
            apt-get update -qq >/dev/null 2>&1
        fi
        if ! command_exists curl; then
            apt-get -y install curl -qq >/dev/null 2>&1
        fi
        if ! command_exists lsb_release; then
            apt-get install lsb-release -qq >/dev/null 2>&1
        fi
        if ! command_exists jq; then
            apt-get install jq -qq >/dev/null 2>&1
        fi
        if ! command_exists gpg; then
            apt-get install gpg -qq >/dev/null 2>&1
        fi
    fi
    if [ "$wo_force_install" = "y" ]; then
        # Check if USER is empty and assign to a variable
        USER_OR_WORDOPS=${USER:-WordOps}

        [ ! -f "$HOME/.gitconfig" ] && {
            bash -c "echo -e \"[user]\n\tname = $USER_OR_WORDOPS\n\temail = root@$HOSTNAME.local\" > $HOME/.gitconfig"
        }
    fi
    if [ -f ./setup.py ]; then
        wo_version_new=$(grep "version='" setup.py | awk -F "'" '{print $2}' 2>&1)
    else
        wo_version_new=$(curl -m 5 --retry 3 -sL https://api.github.com/repos/WordOps/WordOps/releases/latest 2>&1 | jq -r '.tag_name')
    fi

    echo ""
    wo_lib_echo "Welcome to WordOps install/update script ${wo_version_new}"
    echo ""

}

wo_php_fix() {
    if [ -f /lib/systemd/system/mariadb.service ]; then
        systemctl daemon-reload
        systemctl enable mariadb
    fi
}

wo_git_secure_path() {
    if ! grep -q "safe" ~/.gitconfig; then
        echo -e "\n[safe]\n  directory = *" >>~/.gitconfig
    fi
}

###
# 4 - WO MAIN SETUP
###

# create required directories
wo_dir_init
# install lsb_release, curl, gpg and display header
wo_init
# define main variables
wo_init_variables
# remove old repositories
_run wo_clean_repo
_run wo_download_gpg_keys

if [ -z "$wo_force_install" ]; then
    # check distribution support
    wo_check_distro
fi

# wo uninstall script
if [ "$wo_purge" = "y" ]; then
    _run wo_backup_wo "Backing-up WO install"
    _run wo_uninstall "Uninstalling WordOps"
    wo_lib_echo "The WordOps backup files can be found in $WO_BACKUP_FILE"
    exit 0
else
    # 1 - WO already installed
    if [ -x /usr/local/bin/wo ]; then
        _run wo_clean
        _run wo_woconf
        _run wo_fix_kernel
        _run wo_php_fix
        _run wo_update_repo
        # 2 - Migration from EEv3
    else
        if [ -x /usr/local/bin/ee ]; then
            if [ -z "$wo_force_install" ]; then
                echo -e "Migrate from EasyEngine to WordOps (y/n): " && read -r WO_ANSWER
                if [ "$WO_ANSWER" != "y" ] && [ "$WO_ANSWER" != "Y" ]; then
                    wo_lib_error "Not installing WordOps" 1
                fi
            fi
            _run wo_backup_ee "Backing-up EE install"
            _run wo_remove_ee_cron "Removing EasyEngine cronjob"
        fi

    fi
    _run wo_install_dep "Installing wo dependencies"
    _run wo_timesync
    # skip steps if travis
    if [ -z "$wo_travis" ]; then
        #_run wo_download "Downloading WordOps"
        _run wo_sync_db
        _run wo_install "Installing WordOps"
    else
        _run wo_travis_install "Installing WordOps"
    fi
    if [ -x /usr/local/bin/ee ]; then
        _run wo_upgrade_nginx "Upgrading Nginx"
        _run wo_clean_ee "Cleaning previous EasyEngine install"
    fi
    _run wo_install_acme_sh "Running post-install steps"
    _run wo_domain_suffix
    _run wo_update_wp_cli
    _run wo_update_latest
    _run secure_wo_db

    wo sync
    _run wo_git_secure_path

    if [ "$ee_migration" = "1" ]; then
        echo
        wo_lib_echo "The migration from EasyEngine to WordOps was successful!"
        wo_lib_echo "The EasyEngine backup files can be found in /var/lib/wo-backup/ee-backup.tgz"
        echo
    elif [ "$wo_upgrade" = "1" ]; then
        wo_lib_echo "WordOps (wo) upgrade to $wo_version_new was successful!"
        wo_lib_echo "Changelog is available on https://github.com/WordOps/WordOps/releases/tag/$wo_version_new"
        echo
        version_to_check="v3.21"
        if echo "$wo_version_new" | grep -q "$version_to_check"; then
            wo_lib_echo "To upgrade Nginx package and configuration for HTTP/3 QUIC, use the following command"
            wo_lib_echo_info "wo stack migrate --nginx"
        fi
        echo
        wo_lib_echo "To upgrade WordOps web stacks, you can use the command:"
        wo_lib_echo_info "wo stack upgrade"
        echo
        wo_lib_echo "To update all other packages, you can use the command:"
        wo_lib_echo_info "wo maintenance"
    else
        wo_lib_echo "WordOps (wo) installed successfully"
        echo
        wo_lib_echo "To enable bash-completion, just use the command:"
        wo_lib_echo_info "bash -l"
        echo
        wo_lib_echo "To install WordOps recommended stacks, you can use the command:"
        wo_lib_echo_info "wo stack install"
        echo
        wo_lib_echo "To create a first WordPress site, you can use the command:"
        wo_lib_echo_info "wo site create site.tld --wp"
    fi
    echo
    wo_lib_echo "WordOps Documentation : https://docs.wordops.net"
    wo_lib_echo "WordOps Community Forum : https://community.wordops.net"
    echo
    wo_lib_echo "Give WordOps a GitHub star : https://github.com/WordOps/WordOps/"
    echo
fi
