
[nosetests]
verbosity=2
debug=0
detailed-errors=1
with-coverage=1
cover-package=wo
cover-inclusive=1
cover-erase=1
cover-html=1
cover-html-dir=coverage_report/
where=tests/

[metadata]
license-file = LICENSE

[flake8]
ignore = F405,W504,S322,S404,S603,s607,s602,C901,W503
max-line-length = 120
exclude =
    # No need to traverse our git directory
    .git,
    # There's no value in checking cache directories
    __pycache__,
    # This contains our built documentation
    build,
    # This contains builds of flake8 that we don't want to check
    dist
max-complexity = 10
