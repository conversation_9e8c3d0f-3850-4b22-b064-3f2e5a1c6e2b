_wo_complete()
{
    local cur prev BASE_LEVEL

    COMPREPLY=()
    cur=${COMP_WORDS[COMP_CWORD]}
    prev=${COMP_WORDS[COMP_CWORD-1]}
    mprev=${COMP_WORDS[COMP_CWORD-2]}


    # SETUP THE BASE LEVEL (everything after "wo")
    if [ $COMP_CWORD -eq 1 ]; then
        COMPREPLY=( $(compgen \
                      -W "stack site debug clean secure import-slow-log log update sync info maintenance --version --help --quiet" \
                      -- $cur) )


    # SETUP THE SECOND LEVEL (EVERYTHING AFTER "wo second")
    elif [ $COMP_CWORD -eq 2 ]; then
        case "$prev" in

            # HANDLE EVERYTHING AFTER THE SECOND LEVEL NAMESPACE
            "clean")
                COMPREPLY=( $(compgen \
                              -W "--opcache --fastcgi --redis --all" \
                              -- $cur) )
                ;;

            # IF YOU HAD ANOTHER CONTROLLER, YOU'D HANDLE THAT HERE
            "debug")
                COMPREPLY=( $(compgen \
                              -W "$(command find /etc/nginx/sites-enabled/ -type l -printf "%P " 2> /dev/null)  --nginx --php --php73 --fpm --fpm7 --mysql -i --interactive --all --import-slow-log --import-slow-log-interval= --nginx=off --php=off --php73=off --fpm=off --fpm7=off --mysql=off --all=off  " \
                              -- $cur) )
                ;;

            "stack")
                COMPREPLY=( $(compgen \
                              -W "upgrade install migrate purge reload remove restart start status stop" \
                              -- $cur) )
                ;;

            "site")
                COMPREPLY=( $(compgen \
                              -W "cd create delete disable edit enable info list log show update" \
                              -- $cur) )
                ;;

            "secure")
                COMPREPLY=( $(compgen \
                              -W "--auth --port --ip --ssh --sshport" \
                              -- $cur) )
                ;;

            "info")
                COMPREPLY=( $(compgen \
                              -W "--mysql --php --php74 --php80 --php81 --php82 --php83 --php84 --nginx" \
                              -- $cur) )
                ;;

            "log")
                COMPREPLY=( $(compgen \
                              -W "show reset gzip" \
                              -- $cur) )
                ;;

            # EVERYTHING ELSE
            *)
                ;;
        esac

    # SETUP THE THIRD LEVEL (EVERYTHING AFTER "wo second third")
    elif [ $COMP_CWORD -eq 3 ]; then
        case "$prev" in
            # HANDLE EVERYTHING AFTER THE THIRD LEVEL NAMESPACE
            "install" | "purge" | "remove" )
                COMPREPLY=( $(compgen \
                              -W "--web --admin --security --nginx --php74 --php80 --php81 --php82 --php83 --php84 --mysql --wpcli --phpmyadmin --adminer --utils --redis --phpredisadmin --composer --netdata --fail2ban --ufw --dashboard --proftpd --clamav --sendmail --ngxblocker --mysqlclient --mysqltuner --extplorer --nanorc --cheat --brotli --all --force" \
                              -- $cur) )
                ;;
            "upgrade" )
                COMPREPLY=( $(compgen \
                              -W "--web --admin --utils --nginx --php74 --php80 --php81 --php82 --php83 --php84   --mysql --all --netdata --composer --phpmyadmin --adminer --dashboard --mysqltuner --wpcli --force" \
                              -- $cur) )
                ;;
            "migrate")
                COMPREPLY=( $(compgen \
                              -W "--mariadb --nginx --force" \
                              -- $cur) )
                ;;
            "start" | "stop" | "reload" | "restart" | "status")
                COMPREPLY=( $(compgen \
                              -W "--nginx --php --php74 --php80 --php81 --php82 --php83 --php84 --mysql --redis --fail2ban --ufw --netdata -proftpd" \
                              -- $cur) )
                ;;
            "list")
                COMPREPLY=( $(compgen \
                              -W "--enabled --disabled" \
                              -- $cur) )
                ;;

            "edit" | "enable" | "info" | "log" | "show" | "cd" | "delete")
                if [ ""${COMP_WORDS[1]}"" == "log" ]; then
                    COMPREPLY=( $(compgen \
                                  -W "$(find /etc/nginx/sites-available/ -type f -printf "%P " 2> /dev/null) --nginx --php --fpm --mysql --access" \
                                  -- $cur) )
                else
                    COMPREPLY=( $(compgen \
                                -W "$(find /etc/nginx/sites-available/ -type f -printf "%P " 2> /dev/null)" \
                                -- $cur) )
                fi
                ;;
            "update")
                COMPREPLY=( $(compgen \
                            -W "$(find /etc/nginx/sites-available/ -type f -printf "%P " 2> /dev/null) --all" \
                            -- $cur) )
                ;;
            "gzip")
                COMPREPLY=( $(compgen \
                              -W "$(find /etc/nginx/sites-available/ -type f -printf "%P " 2> /dev/null) --nginx --php --fpm --mysql  --access" \
                              -- $cur) )
                ;;

            "reset")
                COMPREPLY=( $(compgen \
                              -W "$(find /etc/nginx/sites-available/ -type f -printf "%P " 2> /dev/null) --nginx --php --fpm --mysql --wp --access --slow-log-db" \
                              -- $cur) )
                ;;

            "disable")
                COMPREPLY=( $(compgen \
                              -W "$(command find /etc/nginx/sites-enabled/ -type l -printf "%P " 2> /dev/null)" \
                              -- $cur) )
                ;;

            *)
                ;;
        esac

        if [ ""${COMP_WORDS[1]}"" == "debug" ] && ([ "$prev" != "--start" ] && [ "$prev" != "--nginx" ] && [ "$prev" != "--php" ] && [ "$prev" != "--php73" ] && [ "$prev" != "--fpm" ] && [ "$prev" != "--fpm7" ] && [ "$prev" != "--mysql" ] && [ "$prev" != "-i" ] && [ "$prev" != "--interactive" ] && [ "$prev" != "--import-slow-log" ] && [ "$prev" != "--stop" ]); then
            retlist="--all --wp --rewrite -i --all=off --wp=off --rewrite=off"
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                          -W "$(echo $ret)" \
                          -- $cur) )
        fi

        #if [ "${COMP_WORDS[1]}" == "log" ] && ([ "$prev" != "--access" ] || [ "$prev" != "--nginx" ] || [ "$prev" != "--php" ] || [ "$prev" != "--fpm" ] || [ "$prev" != "--mysql" ] || [ "$prev" != "-i" ] || ["$prev" != "--interactive" ] || ["$prev" != "--stop" ]); then
        #        retlist="--all --wp --rewrite -i --all=off --wp=off --rewrite=off"
        #        ret="${retlist[@]/$prev}"
        #        COMPREPLY=( $(compgen \
        #                      -W "$(echo $ret)" \
        #                      -- $cur) )
        #fi


    elif [ "$COMP_CWORD" -eq 4 ]; then
        case "$mprev" in
            # HANDLE EVERYTHING AFTER THE THIRD LEVEL NAMESPACE

            "create")
                COMPREPLY=( $(compgen \
                                    -W "--user --pass --email --html --php --php74 --php80 --php81 --php82 --php83 --php84  --mysql --wp  --wpsubdir --wpsubdomain --wpfc --wpsc --proxy= --alias --subsiteof --wpredis --wprocket --wpce -le --letsencrypt  --letsencrypt=wildcard -le=wildcard --dns --dns=dns_cf --dns=dns_dgon" \
                                 -- $cur) )
                ;;

            "update")
                COMPREPLY=( $(compgen \
                                    -W "--password --php --php74 --php80 --php81 --php82 --php83 --php84  --mysql --wp --wpsubdir --wpsubdomain --wpfc --wpsc --wpredis --wprocket --wpce --alias --subsiteof -le -le=off --letsencrypt --letsencrypt=off --letsencrypt=clean -le=wildcard -le=clean --dns --dns=dns_cf --dns=dns_dgon --ngxblocker --ngxblocker=off" \
                                 -- $cur) )
                ;;
            "delete")
                COMPREPLY=( $(compgen \
                                    -W "--db --files --all --no-prompt --force -f" \
                                 -- $cur) )
                ;;
            "show")
                COMPREPLY=( $(compgen \
                              -W "--wp --nginx --php --fpm --mysql  --access" \
                              -- $cur) )
                ;;

            "gzip")
                COMPREPLY=( $(compgen \
                              -W "--wp --nginx --php --fpm --mysql  --access" \
                              -- $cur) )
                ;;

            "mail")
                COMPREPLY=( $(compgen \
                              -W "--wp --nginx --php --fpm --mysql --access --to=" \
                              -- $cur) )
                ;;

            "reset")
                COMPREPLY=( $(compgen \
                              -W "--wp --nginx --php --fpm --mysql --wp --access --slow-log-db" \
                              -- $cur) )
                ;;
            edit)
                COMPREPLY=( $(compgen \

                              -- $cur) )
                ;;
            *)
                ;;
        esac

    fi

    case "$prev" in
        "--wp")
            if [ "${COMP_WORDS[1]}" != "debug" ]; then
                if [ "${COMP_WORDS[2]}" == "create" ]; then
                    retlist="--wp --wpsc --wpfc --user --email --pass --wpredis --wprocket --wpce --letsencrypt -le --letsencrypt=wildcard --dns --dns=dns_cf --dns=dns_dgon --php74 --php80 --php81 --php82 --php83 --php84"
                elif [ "${COMP_WORDS[2]}" == "update" ]; then
                    retlist="--wp --wpfc --wpsc --php74 --php80 --php81 --php82 --php83 --php84  --wpredis --wprocket --wpce -le --letsencrypt --letsencrypt=wildcard -le=wildcard --dns --dns=dns_cf --dns=dns_dgon"
                else
                    retlist=""
                fi
            else
                retlist="--wp --wp=off --rewrite --rewrite=off -i --interactive"
            fi

            ret="${retlist[@]/$prev}"
                COMPREPLY=( $(compgen \
                              -W "$(echo $ret)" \
                              -- $cur) )
            ;;

        "--wpsubdir" | "--wpsubdomain")
            if [ "${COMP_WORDS[1]}" != "debug" ]; then
                if [ "${COMP_WORDS[2]}" == "create" ]; then
                    retlist="--wpsc --wpfc --user --email --pass --wpredis --wprocket --wpce  -le -le=wildcard --letsencrypt --letsencrypt=wildcard --php74 --php80 --php81 --php82 --php83 --php84 --dns --dns=dns_cf --dns=dns_dgon"
                elif [ "${COMP_WORDS[2]}" == "update" ]; then
                    retlist="--wpfc --wpsc --php74 --php80 --php81 --php82 --php83 --php84 --wpredis --wprocket --wpce -le -le=wildcard --letsencrypt --letsencrypt=wildcard --dns --dns=dns_cf --dns=dns_dgon"
                else
                    retlist=""
                fi
            else
                retlist="--wp=off --rewrite --rewrite=off -i --interactive"
            fi

            ret="${retlist[@]/$prev}"
                COMPREPLY=( $(compgen \
                              -W "$(echo $ret)" \
                              -- $cur) )
            ;;

        "--wpredis" | "--wprocket" | "--wpce" | "--wpfc" | "--wpsc" | "--wpsubdir" | "--wpsubdomain" | "--user" | "--pass" | "--email" | "--wp")
            if [ "${COMP_WORDS[2]}" == "create" ]; then
                retlist="--user --pass --email --wp  --wpsubdir --wpsubdomain --wpfc --wpsc --wpredis --wprocket --wpce --php74 --php80 --php81 --php82 --php83 --php84 -le -le=wildcard --letsencrypt --letsencrypt=wildcard --dns --dns=dns_cf --dns=dns_dgon"
            else
                retlist=""
            fi

            ret="${retlist[@]/$prev}"
                COMPREPLY=( $(compgen \
                              -W "$(echo $ret)" \
                              -- $cur) )
            ;;

        "--wpredis" | "--wprocket" | "--wpce" | "--wpfc" | "--wpsc")
            if [ "${COMP_WORDS[2]}" == "update" ]; then
                retlist="--password --php74 --php80 --php81 --php82 --php83 --php84 --mysql --wp  --wpsubdir --wpsubdomain -le --letsencrypt --dns --dns=dns_cf --dns=dns_dgon"
            else
                retlist=""| "--php82" | "--php83 --php84"
            fi

            ret="${retlist[@]/$prev}"
                COMPREPLY=( $(compgen \
                              -W "$(echo $ret)" \
                              -- $cur) )
            ;;

        "--web" | "--admin" | "--nginx" | "--php" | "--php74" | "--php80" | "--php81" | "--php82" | "--php83" | "--php84" | "--mysql" | "--wpcli" | "--phpmyadmin" | "--adminer" | "--utils" | "--fail2ban" | "--ufw" | "--redis" | "--phpredisadmin" | "--netdata" | "--sendmail" | "--composer" | "--proftpd" | "--cheat" | "--nanorc" | "--clamav" | "--dashboard")
            if [[ "${COMP_WORDS[2]}" == "install" || "${COMP_WORDS[2]}" == "purge" || "${COMP_WORDS[2]}" == "remove" ]]; then
                retlist="--web --admin --security --nginx --php --php74 --php80 --php81 --php82 --php83 --php84 --mysql --wpcli --phpmyadmin --adminer --utils --redis --fail2ban --ufw --phpredisadmin --netdata --force"
            elif [[ "${COMP_WORDS[2]}" == "start" || "${COMP_WORDS[2]}" == "reload" || "${COMP_WORDS[2]}" == "restart" || "${COMP_WORDS[2]}" == "stop" ]]; then
                    retlist="--nginx --php  --php74 --php80 --php81 --php82 --php83 --php84 --mysql --redis --netdata --fail2ban --ufw"
            elif [[ "${COMP_WORDS[1]}" == "debug" ]]; then
                    retlist="--start --nginx --php --php73 --fpm --fpm7 --mysql -i --interactive -stop --import-slow-log --import-slow-log-interval= -"
                    if [[ $prev == '--mysql' ]]; then
                        retlist="--start --nginx --php --php73 --fpm --fpm7 --mysql -i --interactive --stop --import-slow-log"
                    fi
            elif [[ "${COMP_WORDS[1]}" == "log" ]]; then
                    if [ "${COMP_WORDS[2]}" == "show" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp"

                    elif [ "${COMP_WORDS[2]}" == "reset" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp --slow-log-db"

                    elif [ "${COMP_WORDS[2]}" == "mail" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp --to="

                    fi
            fi
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                        -W "$(echo $ret)" \
                        -- $cur) )
            ;;

        "--db" | "--files" | "--force")
            retlist="--db --files --all --force"
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                          -W "$(echo $ret)" \
                          -- $cur) )
            ;;

        "--all")
          if [ "${COMP_WORDS[1]}" == "clean" ]; then
                  retlist="--opcache --fastcgi --redis"
          elif [ "${COMP_WORDS[2]}" == "delete" ]; then
              retlist="--db --files --force"
          elif [ "${COMP_WORDS[2]}" == "update" ]; then
            retlist="--password --php --mysql --wp --wpsubdir --wpsubdomain --wpfc --wpsc --wpredis --wprocket --wpce -le -le=off -le=wildcard --letsencrypt --letsencrypt=off "
          else
            retlist=""
          fi
          ret="${retlist[@]/$prev}"
              COMPREPLY=( $(compgen \
                            -W "$(echo $ret)" \
                            -- $cur) )
          ;;

        "--opcache" | "--fastcgi" | "--all" | "--redis")
            retlist="--opcache --fastcgi --redis --all"
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                          -W "$(echo $ret)" \
                          -- $cur) )
            ;;
        "--auth" | "--port" | "--ip" | "--ssh" | "--sshport")
            retlist="--auth --port --ip --ssh --sshport"
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                          -W "$(echo $ret)" \
                          -- $cur) )
            ;;
        "--access" | "--fpm" | "--slow-log-db")
            if [[ "${COMP_WORDS[1]}" == "log" ]]; then
                    if [ "${COMP_WORDS[2]}" == "show" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp"

                    elif [ "${COMP_WORDS[2]}" == "reset" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp --slow-log-db"


                    elif [ "${COMP_WORDS[2]}" == "mail" ]; then
                        retlist="--access --nginx --php --mysql --fpm --wp --to="

                    fi
            fi
            ret="${retlist[@]/$prev}"
            COMPREPLY=( $(compgen \
                          -W "$(echo $ret)" \
                          -- $cur) )
            ;;
        *)
            ;;
        esac
        case "$mprev" in
            "--user" | "--email" | "--pass")
                if [ "${COMP_WORDS[2]}" == "create" ]; then
                    retlist="--user --pass --email --html --php --php74 --php80 --php81 --php82 --php83 --php84 --mysql --wp  --wpsubdir --wpsubdomain --wpfc --wpsc --wpredis --wprocket --wpce -le -le=wildcard --letsencrypt --letsencrypt=wildcard --dns --dns=dns_cf --dns=dns_dgon"
                fi
                ret="${retlist[@]/$prev}"
                COMPREPLY=( $(compgen \
                              -W "$(echo $ret)" \
                              -- $cur) )
                ;;
        esac

    return 0

} &&
complete -F _wo_complete wo
