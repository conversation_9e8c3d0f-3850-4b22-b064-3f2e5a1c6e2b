# WordOps Configuration
#
# All commented values are the application default
#

[wo]

### Toggle application level debug (does not toggle framework debugging)
# debug = false

### Where external (third-party) plugins are loaded from
# plugin_dir = /var/lib/wo/plugins/

### Where all plugin configurations are loaded from
# plugin_config_dir = /etc/wo/plugins.d/

### Where external templates are loaded from
# template_dir = /var/lib/wo/templates/


[log.colorlog]

### Where the log file lives (no log file by default)
file = /var/log/wo/wordops.log

### The level for which to log.  One of: info, warn, error, fatal, debug
level = debug

### Whether or not to log to console
to_console = false

### Whether or not to rotate the log file when it reaches `max_bytes`
rotate = true

### Max size in bytes that a log file can grow until it is rotated.
max_bytes = 1000000

### The maximun number of log files to maintain when rotating
max_files = 7

colorize_file_log = true

colorize_console_log = true

[stack]

### IP address that will be used in Nginx configurations while installing
ip-address = 127.0.0.1

[mysql]

### MySQL database grant host name
grant-host = localhost

### Ask for MySQL db name while site creation
db-name = False

### Ask for MySQL user name while site creation
db-user = False

[wordpress]

### Ask for WordPress prefix while site creation
prefix = False

### User name for WordPress sites
user =

### Password for WordPress sites
password =

### EMail for WordPress sites
email =

[letsencrypt]

keylength = "ec-384"

[php]

### Default PHP version
version = 8.3

[mariadb]

### Default MariaDB release
release = 11.4

[update]

### If enabled, load a plugin named `update` either from the Python module
### `wo.cli.plugins.example` or from the file path
### `/var/lib/wo/plugins/example.py`
enable_plugin = true

[sync]
### If enabled, load a plugin named `update` either from the Python module
### `wo.cli.plugins.example` or from the file path
### `/var/lib/wo/plugins/example.py`
enable_plugin = true
