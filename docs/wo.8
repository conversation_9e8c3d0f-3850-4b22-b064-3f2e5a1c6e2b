.TH wo 8 "WordOps (wo) version: 3.10.0" "Oct 24,2019" "WordOps"
.SH NAME
.B WordOps (wo)
\- Manage Nginx Based Websites.
.SH SYNOPSIS
wo [ --version | --help | info | stack | site | debug | update | clean | import_slow_log | log | secure | sync | maintenance ]
.TP
wo stack [ install | remove | purge | migrate | upgrade ] [ --web | --all | --nginx | --php | --php73 | --mysql | --admin | --adminer | --redis | --phpmyadmin | --phpredisadmin | --wpcli | --utils | --dashboard | --netdata | --fail2ban | --proftpd ]
.TP
wo stack [ status | start | stop | reload | restart ] [--all | --nginx | --php | --php73 |--mysql | --web | --redis | --netdata | --fail2ban | --proftpd]
.TP
wo site [ list | info | show | enable | disable | edit | cd | show ] [ example.com ]
.TP
wo site create example.com [ --html | --php | --php73 | --mysql][[--wp | --wpsubdir | --wpsubdomain ] [ --wpsc | --wpfc | --wpredis | --wpce | --wprocket ] [ -le/--letsencrypt=wildcard ][ --dns/--dns=dns_cf/dns_dgon]]
.TP
wo site update example.com [ --php | --php73 |--mysql] [[--wp | --wpsubdir | --wpsubdomain ] [--wpsc | --wpfc | --wpredis | --wpce | --wprocket ] [--password] [-le/--letsencrypt=on/off/wildcard/clean/purge ] [ --dns/--dns=dns_cf/dns_dgon ]
.TP
wo site delete example.com [--db | --files | --all | --no-prompt | --force ]
.TP
wo debug [ -i | --all=on/off |--nginx=on/off | --rewrite=on/off | --php=on/off | --fpm=on/off | --mysql=on/off ]
.TP
wo debug example.com [ -i | --all=on/off | --nginx=on/off | --rewrite=on/off | --wp=on/off ]
.TP
wo secure [ --auth | --port | --ip | --ssh | --sshport ]
.SH DESCRIPTION
WordOps aka wo is the opensource project developed with the purpose to automate web-server configuration.
.br
WordOps is the collection of python script that provides automation for the web-server
.br
installation, site creation, services debugging & monitoring.
.SH OPTIONS
.TP
.B --version
.br
Display WordOps (wo) version information.
.TP
.B info
.br
wo info - Display Nginx, PHP, MySQL and wo common location information
.br
wo site info - Diplay given website details like enable, disable. weboot and log files.
.TP
.B --help
.br
Display WordOps (wo) help.
.TP
.B stack
.TP
.B install [ --all | --web | --nginx | --php | --php73 |--mysql | --redis | --adminer | --phpmyadmin | --phpredismyadmin | --wpcli | --utils | --netdata | --dashboard | --fail2ban | --proftpd ]
.br
Install Nginx PHP7.2 MariaDB SendMail Netdata Fail2Ban stack Packages if not used with
.br
any options.Installs specific package if used with option.
.TP
.B remove [ --all | --web | --nginx | --php | --php73 |--mysql | --redis | --adminer | --phpmyadmin | --phpredismyadmin | --wpcli | --utils | --netdata | --dashboard | --fail2ban | --proftpd ]
.br
Remove Nginx PHP5 MySQL Postfix stack Packages if not used with
.br
any options. Remove specific package if used with option.
.TP
.B purge [ --all | --web | --nginx | --php | --php73 |--mysql | --redis | --adminer | --phpmyadmin | --phpredismyadmin | --wpcli | --utils | --netdata | --dashboard | --fail2ban | --proftpd ]
.br
Purge Nginx PHP5 MySQL Postfix stack Packages if not used with any
.br
options.Purge specific package if used with option.
.TP
.B status
.br
Display status of NGINX, PHP7.2-FPM, MySQL, Redis-Server services.
.TP
.B start
.br
Start services NGINX, PHP7.2-FPM, MySQL, Redis-Server.
.TP
.B stop
.br
Stop services NGINX, PHP7.2-FPM, MySQL, Redis-Server.
.TP
.B reload
.br
Reload services NGINX, PHP7.2-FPM, MySQL, Redis-Server.
.TP
.B restart
.br
Restart services NGINX, PHP7.2-FPM, MySQL, Redis-Server.
.TP
.B site
.br
.TP
.B cd [ example.com ]
.br
Change directory to webroot of specified site in subshell.
.TP
.B log [ example.com ]
.br
monitor access and error logs for site specified.
.TP
.B list [ --enabled | --disabled ]
.br
Lists all available sites from /etc/nginx/sites-enabled/
.br
by default & enable argument. Display sites list from
.br
/etc/nginx/sites-available/ if used with available option.
.TP
.B info [ example.com ]
.br
prints information about site such as access log, error log
.br
location and type of site.
.TP
.B show [ example.com ]
.br
Display NGINX configuration of site.
.TP
.B enable [ example.com ]
.br
Enable site by creating softlink with site file in
.br
/etc/nginx/sites-available to /etc/nginx/sites-enabled/.
.TP
.B disable [ example.com ]
.br
Disable site by Destroying softlink with site file in
.br
/etc/nginx/sites-available to /etc/nginx/sites-enabled/.
.TP
.B edit [ example.com ]
.br
Edit NGINX configuration of site.
.TP
.B create [ example.com ] [ --html | --php | --php73 |--mysql] [[--wp | --wpsubdir | --wpsubdomain ] [--wpsc | --wpfc | --wpredis ]
.br
Create new site according to given options. If no options provided
.br
create static site with html only.
.TP
.B update [ example.com ] [ --html | --php | --php73 |--mysql] [[--wp | --wpsubdir | --wpsubdomain ] [ --wpsc | --wpfc | --wpredis ] [--password ]
.br
Update site configuration according to specified options.
.TP
.B delete [ example.com ] [--no-prompt ] [--force/-f] [ --db | --files | --all ]
.br
Delete site i.e webroot, database, ad configuration permanently.
.TP
.B debug [ -i | --nginx=on/off | --php=on/off | --php73=on/off | --mysql=on/off | --rewrite=on/off | --fpm=on/off | --fpm7=on/off ]
.br
Starts server level debugging. If this is used without arguments it will start debugging
.br
all services.Else it will debug only service provided with argument.This will Stop
.br
Debugging if used with --all=off argument.
.TP
.B debug example.com [ -i | --nginx=on/off | --rewrite=on/off | --wp=on/off | --all=on/off ]
.br
Starts site level debugging. If this is used without arguments it will start debugging all
.br
services.Else it will debug only service provided with argument.This will Stop Debugging
.br
if used with --all=off argument.
.TP
.B secure [ --auth | --port | --ip ]
.br
Update security settings.
.TP
.B clean [ --fastcgi | --opcache | --redis | --all ]
.br
Clean NGINX fastCGI cache, Opcache, Redis cache.
.br
Clean NGINX fastCGI cache if no option specified.
.SH ARGUMENTS
.TP
.B -i
.br
setup intractive mode while used with debug.
.TP
.B --nginx=on/off
.br
used with wo debug command. used to start or stop nginx debugging.
.TP
.B --php=on/off
.br
used with wo debug command. used to start or stop php debugging.
.TP
.B --php73=on/off
.br
used with wo debug command. used to start or stop php72 debugging.
.TP
.B --mysql=on/off
.br
used with wo debug command. used to start or stop mysql debugging.
.TP
.B --rewrite=on/off
.br
used with wo debug command. used to start or stop nginx rewrite rules debugging.
.TP
.B --fpm=on/off
.br
used with wo debug command. used to start or stop fpm debugging.
.TP
.B --wp=on/off
.br
used with wo debug command. used to start or stop  wordpress site debugging.
.TP
.B --all=on/off
.br
used with wo debug command. used to stop debugging.
.TP
.B --all=off
.br
used with wo debug command. used to stop debugging.
.TP
.B --html
.br
Create a HTML website.
.TP
.B --php
.br
Create a PHP website.
.TP
.B --mysql
.br
Create a PHP+MySQL website.
.TP
.B --wp
.br
Create a WordPress Website.
.TP
.B --wpsubdir
.br
Create a Wordpress Multisite with Sub Directories Setup.
.TP
.B --wpsubdomain
.br
Create a Wordpress Multisite with Sub Domains Setup.
.br
.TP
.B --db
.br
Delete website database.
.br
.TP
.B --files
.br
Delete website webroot.
.br
.TP
.B --no-prompt
.br
Does not prompt for confirmation when delete command used.
.br
.TP
.B --force/-f
.br
Delete website webroot and database forcefully.Remove nginx configuration for site.
.br
.TP
.B --auth
.br
used with wo secure command. Update credential of HTTP authentication
.TP
.B --port
.br
used with wo secure command. Change WordOps admin port 22222.
.TP
.B --ip
.br
used with wo secure command. Update whitelist IP address
.SH WORDPRESS CACHING OPTIONS
.TP
.B --wpsc
.br
Install and activate WP Super Cache plugin and serve pages from cache directly with Nginx.
.TP
.B --wpfc
.br
Install and activate Nginx-helper plugin with Nginx FastCGI cache.
.TP
.B --wpredis
.br
Install, activate, configure Nginx-helper and Redis Object Cache Plugin, Configure NGINX for Redis Full-Page Caching.
.TP
.B --wpce
.br
Install and activate Cache-enabler plugin and serve pages from cache directly with Nginx.
.TP
.B --wprocket
.br
Configure Nginx for WP-Rocket plugin to serve pages from cache directly with Nginx.
.SH FILES
.br
/etc/wo/wo.conf
.SH BUGS
Report bugs at <http://github.com/WordOps/WordOps/issues/>
.SH AUTHOR
.br
.B rtCamp Team
.I \<<EMAIL>\>
.br
.B Mitesh Shah
.I \<<EMAIL>\>
.br
.B Manish
.I \<<EMAIL>\>
.br
.B Gaurav
.I \<<EMAIL>\>
.br
.B Harshad
.I \<<EMAIL>>
.br
.B Prabuddha
.I \<<EMAIL>\>
.br
.B Shital
.I \<<EMAIL>\>
.br
.B Rajdeep Sharma
.I \<<EMAIL>\>
.br
.B Thomas SUCHON
.I \<<EMAIL>\>
.br
